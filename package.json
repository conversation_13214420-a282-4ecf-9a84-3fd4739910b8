{"name": "goshop", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "redux", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@tarojs/components": "4.1.1", "@tarojs/helper": "4.1.1", "@tarojs/plugin-platform-weapp": "4.1.1", "@tarojs/plugin-platform-alipay": "4.1.1", "@tarojs/plugin-platform-tt": "4.1.1", "@tarojs/plugin-platform-swan": "4.1.1", "@tarojs/plugin-platform-jd": "4.1.1", "@tarojs/plugin-platform-qq": "4.1.1", "@tarojs/plugin-platform-h5": "4.1.1", "@tarojs/runtime": "4.1.1", "@tarojs/shared": "4.1.1", "@tarojs/taro": "4.1.1", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "redux": "^4.0.0", "react-redux": "^7.2.0", "@tarojs/plugin-framework-react": "4.1.1", "@tarojs/react": "4.1.1", "react-dom": "^18.0.0", "react": "^18.0.0"}, "devDependencies": {"@babel/preset-react": "^7.24.1", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/core": "^7.8.0", "@tarojs/cli": "4.1.1", "@types/webpack-env": "^1.13.6", "@types/react": "^18.0.0", "webpack": "5.91.0", "@tarojs/taro-loader": "4.1.1", "@tarojs/webpack5-runner": "4.1.1", "babel-preset-taro": "4.1.1", "eslint-config-taro": "4.1.1", "eslint": "^8.12.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "react-refresh": "^0.11.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react-hooks": "^4.2.0", "stylelint": "^14.4.0", "@typescript-eslint/parser": "^6.2.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "typescript": "^5.1.0", "tsconfig-paths-webpack-plugin": "^4.0.1", "postcss": "^8.4.18", "ts-node": "^10.9.1", "@types/node": "^18.15.11"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}